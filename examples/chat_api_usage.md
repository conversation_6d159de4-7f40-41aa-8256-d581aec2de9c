# Chat API 使用示例

## 新的多轮对话支持

现在 Chat API 支持多轮对话，前端可以发送完整的对话历史。

### API 端点
```
POST /v1/chat/stream
```

### 请求格式

#### 单轮对话
```json
{
  "messages": [
    {
      "role": "user",
      "content": "帮我做一个博客网站"
    }
  ]
}
```

#### 多轮对话
```json
{
  "messages": [
    {
      "role": "user", 
      "content": "帮我做一个博客网站"
    },
    {
      "role": "assistant",
      "content": "好的，我来帮你创建一个博客网站。我会使用现代的技术栈来构建..."
    },
    {
      "role": "user",
      "content": "请把主题颜色改成蓝色"
    }
  ]
}
```

#### 带工具调用的对话
```json
{
  "messages": [
    {
      "role": "user",
      "content": "帮我做一个React博客，需要安装必要的依赖"
    }
  ],
  "toolsParam": {
    "auto": ["web-coder-npmInstall", "search"]
  }
}
```

### 消息角色说明

- `user`: 用户消息
- `assistant`: AI助手回复
- `system`: 系统提示（通常由后端自动添加）

### 前端实现建议

```javascript
class ChatManager {
  constructor() {
    this.messages = [];
  }

  async sendMessage(userMessage) {
    // 添加用户消息到历史
    this.messages.push({
      role: "user",
      content: userMessage
    });

    // 发送请求
    const response = await fetch('/v1/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        messages: this.messages
      })
    });

    // 处理流式响应
    const reader = response.body.getReader();
    let assistantMessage = "";
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = new TextDecoder().decode(value);
      // 处理 SSE 数据
      assistantMessage += chunk;
    }

    // 添加助手回复到历史
    this.messages.push({
      role: "assistant", 
      content: assistantMessage
    });
  }
}
```

### 优势

1. **完整上下文**: AI 可以理解整个对话历史
2. **连续对话**: 支持"修改颜色"、"添加功能"等后续请求
3. **前端控制**: 前端完全控制对话历史和上下文
4. **简洁设计**: 后端无需存储会话状态
5. **灵活性**: 前端可以选择性发送历史消息

### 注意事项

1. **Token 限制**: 注意控制消息历史长度，避免超过模型的 token 限制
2. **性能考虑**: 对话历史过长会影响响应速度
3. **内存管理**: 前端需要合理管理消息历史的内存使用
