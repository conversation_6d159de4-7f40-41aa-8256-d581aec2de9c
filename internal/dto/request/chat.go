package request

import (
	"encoding/json"
	"fmt"
	"github.com/Sider-ai/go-pkg/documentmodel"
)

// ChatRequest represents the chat request from client
type ChatRequest struct {
	Prompt          string                         `json:"prompt" binding:"required"`
	PromptTemplates []documentmodel.PromptTemplate `json:"promptTemplates,omitempty"`
	ToolsParam      *ToolsParam                    `json:"toolsParam,omitempty"`
}

// ToolsParam represents the tools configuration
type ToolsParam struct {
	Auto []string `json:"auto,omitempty"`
}

// GetAppName implements the AppNameGetter interface
func (r *ChatRequest) GetAppName() string {
	return "web-coder-app"
}

// SetIPInfo implements the IPInfoSetter interface
func (r *ChatRequest) SetIPInfo(ipInfo *IPInfo) {

}

func (r *ChatRequest) Validate() error {
	if r.Prompt == "" {
		return fmt.Errorf("prompt is required")
	}

	return nil
}

// ToJSON converts the request to JSON string for logging
func (r *ChatRequest) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}

// GetSystemPrompt builds the system prompt from templates
func (r *ChatRequest) GetSystemPrompt() string {
	if len(r.PromptTemplates) == 0 {
		return ""
	}

	var systemPrompt string
	for _, template := range r.PromptTemplates {
		switch template.Key {
		case "agent_webpage_react":
			systemPrompt += r.buildWebpageReactPrompt(template.Attributes)
		default:
			// Handle unknown templates or log warning
		}
	}

	return systemPrompt
}

// buildWebpageReactPrompt builds the webpage react agent prompt
func (r *ChatRequest) buildWebpageReactPrompt(attributes map[string]string) string {
	language := "English"
	if lang, ok := attributes["language"]; ok {
		language = lang
	}

	code := ""
	if c, ok := attributes["code"]; ok {
		code = c
	}

	prompt := fmt.Sprintf(`You are a web development assistant that helps users create and modify web pages. 

Language: %s

You have access to the following tools:
- search: Search for information on the web
- web-coder-npmInstall: Install npm packages
- web-coder-npmUninstall: Uninstall npm packages

When working with code, please think step by step and explain your reasoning.

Current code context:
%s

Please respond in %s and provide clear explanations for your actions.`, language, code, language)

	return prompt
}

// GetTools returns the available tools based on the request
func (r *ChatRequest) GetTools() []map[string]interface{} {
	if r.ToolsParam == nil || len(r.ToolsParam.Auto) == 0 {
		return nil
	}

	var tools []map[string]interface{}

	for _, toolName := range r.ToolsParam.Auto {
		switch toolName {
		case "search":
			tools = append(tools, map[string]interface{}{
				"name":        "search",
				"description": "Search for information on the web",
				"input_schema": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"query": map[string]interface{}{
							"type":        "string",
							"description": "The search query",
						},
					},
					"required": []string{"query"},
				},
			})
		case "web-coder-npmInstall":
			tools = append(tools, map[string]interface{}{
				"name":        "web-coder-npmInstall",
				"description": "Install npm packages",
				"input_schema": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"packages": map[string]interface{}{
							"type":        "array",
							"items":       map[string]interface{}{"type": "string"},
							"description": "List of npm packages to install",
						},
						"dev": map[string]interface{}{
							"type":        "boolean",
							"description": "Whether to install as dev dependencies",
							"default":     false,
						},
					},
					"required": []string{"packages"},
				},
			})
		case "web-coder-npmUninstall":
			tools = append(tools, map[string]interface{}{
				"name":        "web-coder-npmUninstall",
				"description": "Uninstall npm packages",
				"input_schema": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"packages": map[string]interface{}{
							"type":        "array",
							"items":       map[string]interface{}{"type": "string"},
							"description": "List of npm packages to uninstall",
						},
					},
					"required": []string{"packages"},
				},
			})
		}
	}

	return tools
}

// FunctionCallResultRequest represents the request for function call result
type FunctionCallResultRequest struct {
	CallID  string `json:"callID" binding:"required"`
	Message string `json:"message" binding:"required"`
}

// GetAppName implements the AppNameGetter interface
func (r *FunctionCallResultRequest) GetAppName() string {
	return "web-coder-app"
}

// SetIPInfo implements the IPInfoSetter interface
func (r *FunctionCallResultRequest) SetIPInfo(ipInfo *IPInfo) {
	// Function call result requests don't need IP info stored in the request
	// but we implement this to satisfy the interface
}

// Validate validates the function call result request
func (r *FunctionCallResultRequest) Validate() error {
	if r.CallID == "" {
		return fmt.Errorf("callID is required")
	}
	if r.Message == "" {
		return fmt.Errorf("message is required")
	}
	return nil
}

// ToJSON converts the request to JSON string for logging
func (r *FunctionCallResultRequest) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}
