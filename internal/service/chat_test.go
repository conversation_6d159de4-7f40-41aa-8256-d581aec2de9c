package service

import (
	"context"
	"testing"

	"go.uber.org/zap"

	"web-coder-app/internal/dto/request"
	"web-coder-app/pkg/litellm"
)

func TestChatService_buildLiteLLMRequest(t *testing.T) {
	// Create a mock logger
	logger := zap.NewNop().Sugar()

	// Create a mock LiteLLM client
	client := litellm.New()

	// Create ChatService
	service := &ChatToolsService{
		llmClient: client,
		logger:    logger,
	}

	// Test data - simple chat request
	req := &request.ChatRequest{
		Prompt: "做一个博客",
	}

	// Test buildLiteLLMRequest
	llmReq := service.buildLiteLLMRequest(req)

	// Verify the request
	if llmReq.Model != "claude-4-sonnet" {
		t.Errorf("Expected model 'claude-4-sonnet', got '%s'", llmReq.Model)
	}

	if !llmReq.Stream {
		t.Error("Expected Stream to be true")
	}

	if len(llmReq.Messages) != 1 {
		t.Errorf("Expected 1 message, got %d", len(llmReq.Messages))
	}

	if llmReq.Messages[0].Role != "user" {
		t.Errorf("Expected role 'user', got '%s'", llmReq.Messages[0].Role)
	}

	if llmReq.Messages[0].Content != "Hello, how are you?" {
		t.Errorf("Expected content 'Hello, how are you?', got '%s'", llmReq.Messages[0].Content)
	}
}

func TestChatService_executeTool(t *testing.T) {
	// Create a mock logger
	logger := zap.NewNop().Sugar()

	// Create a mock LiteLLM client
	client := litellm.New()

	// Create ChatService
	service := &ChatToolsService{
		llmClient: client,
		logger:    logger,
	}

	// Test search tool
	ctx := context.Background()
	arguments := `{"query": "test search"}`

	result, err := service.executeTool(ctx, "search", arguments)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Error("Expected result to not be nil")
	}

	// Test unknown tool
	_, err = service.executeTool(ctx, "unknown_tool", arguments)
	if err == nil {
		t.Error("Expected error for unknown tool")
	}
}
