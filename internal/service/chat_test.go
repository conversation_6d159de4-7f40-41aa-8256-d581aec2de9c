package service

import (
	"context"
	"testing"

	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/dto/request"
	"web-coder-app/pkg/litellm"
)

func TestChatService_buildLiteLLMRequest(t *testing.T) {
	// Create a mock logger
	logger := zap.NewNop().Sugar()

	// Create a mock LiteLLM client
	client := litellm.New()

	// Create ChatService
	service := &ChatToolsService{
		llmClient: client,
		logger:    logger,
		conf: &configs.Config{
			ChatModel: configs.ChatModel{
				Chat: "claude-4-sonnet",
			},
		},
	}

	// Test data - simple chat request
	req := &request.ChatRequest{
		Messages: []request.ChatMessage{
			{
				Role:    "user",
				Content: "做一个博客",
			},
		},
	}

	// Test buildLiteLLMRequest
	llmReq := service.buildLiteLLMRequest(req)

	// Verify the request
	if llmReq.Model != "claude-4-sonnet" {
		t.Errorf("Expected model 'claude-4-sonnet', got '%s'", llmReq.Model)
	}

	if !llmReq.Stream {
		t.Error("Expected Stream to be true")
	}

	// Should have at least the user message
	if len(llmReq.Messages) < 1 {
		t.Errorf("Expected at least 1 message, got %d", len(llmReq.Messages))
	}

	// Find the user message
	var userMessage *litellm.Message
	for _, msg := range llmReq.Messages {
		if msg.Role == "user" {
			userMessage = &msg
			break
		}
	}

	if userMessage == nil {
		t.Error("Expected to find a user message")
	} else if userMessage.Content != "做一个博客" {
		t.Errorf("Expected content '做一个博客', got '%s'", userMessage.Content)
	}
}

func TestChatService_executeTool(t *testing.T) {
	// Create a mock logger
	logger := zap.NewNop().Sugar()

	// Create a mock LiteLLM client
	client := litellm.New()

	// Create ChatService
	service := &ChatToolsService{
		llmClient: client,
		logger:    logger,
		conf: &configs.Config{
			ChatModel: configs.ChatModel{
				Chat: "claude-4-sonnet",
			},
		},
	}

	// Test search tool
	ctx := context.Background()
	arguments := `{"query": "test search"}`

	result, err := service.executeTool(ctx, "search", arguments)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Error("Expected result to not be nil")
	}

	// Test unknown tool
	_, err = service.executeTool(ctx, "unknown_tool", arguments)
	if err == nil {
		t.Error("Expected error for unknown tool")
	}
}
